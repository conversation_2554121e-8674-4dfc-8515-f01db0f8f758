// Copyright 2023, the hate<PERSON><PERSON>b project author.
// All rights reserved. Use of this source code is governed by a
// MIT license that can be found in the LICENSE file.

import 'dart:async';
import 'package:app_links/app_links.dart';
import 'package:flutter/material.dart';
import 'package:super_up_core/super_up_core.dart';
import '../../modules/peer_profile/views/peer_profile_view.dart';
import '../../modules/public_profile/views/public_profile_view.dart';
import '../../../main.dart';

class DeepLinkService {
  static final DeepLinkService _instance = DeepLinkService._internal();
  factory DeepLinkService() => _instance;
  DeepLinkService._internal();

  late AppLinks _appLinks;
  StreamSubscription<Uri>? _linkSubscription;

  Future<void> initialize() async {
    _appLinks = AppLinks();

    // Handle app launch from deep link
    final initialUri = await _appLinks.getInitialLink();
    if (initialUri != null) {
      _handleDeepLink(initialUri);
    }

    // Handle deep links while app is running
    _linkSubscription = _appLinks.uriLinkStream.listen(
      (uri) {
        _handleDeepLink(uri);
      },
      onError: (err) {
        print('Deep link error: $err');
      },
    );
  }

  void _handleDeepLink(Uri uri) {
    print('Handling deep link: $uri');

    // Check if it's a profile link from any domain (production or local development)
    if (uri.pathSegments.length >= 2 && uri.pathSegments[0] == 'profile') {
      final userId = uri.pathSegments[1];
      _navigateToProfile(userId);
    }
  }

  void _navigateToProfile(String userId) {
    // Get the current context from the navigator key
    final context = navigatorKey.currentContext;
    if (context != null) {
      // Check if user is authenticated
      bool isAuthenticated = false;
      try {
        AppAuth.myProfile; // This will throw if not authenticated
        isAuthenticated = true;
      } catch (e) {
        isAuthenticated = false;
      }

      // Navigate to appropriate profile page
      Navigator.of(context).push(
        MaterialPageRoute(
          builder: (context) => isAuthenticated
              ? PeerProfileView(peerId: userId)
              : PublicProfileView(userId: userId),
        ),
      );
    }
  }

  void dispose() {
    _linkSubscription?.cancel();
  }
}
