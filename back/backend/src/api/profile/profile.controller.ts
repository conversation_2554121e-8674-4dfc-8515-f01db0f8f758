/**
 * Copyright 2023, the hate<PERSON><PERSON>b project author.
 * All rights reserved. Use of this source code is governed by a
 * MIT license that can be found in the LICENSE file.
 */

import {
    Controller,
    Get,
    UseGuards,
    Req,
    Param,
    Patch,
    Body,
    UseInterceptors,
    BadRequestException, UploadedFile, Post, Query, Delete, Version, Res
} from "@nestjs/common";
import {ProfileService} from "./profile.service";

import {
    UpdateChatReqStatusDto,
    UpdateMyBioDto,
    UpdateMyNameDto,
    UpdateMyPasswordDto,
    UpdateMyPhoneNumberDto,
    UpdateMyPrivacyDto
} from "./dto/update.my.name.dto";
import UpdatePasswordDto from "./dto/update_password_dto";
import {VerifiedAuthGuard} from "../../core/guards/verified.auth.guard";
import {resOK} from "../../core/utils/res.helpers";
import {imageFileInterceptor} from "../../core/utils/upload_interceptors";
import {MongoIdDto} from "../../core/common/dto/mongo.id.dto";
import CheckVersionDto from "./dto/check-version.dto";
import {V1Controller} from "../../core/common/v1-controller.decorator";
import {MongoPeerIdDto} from "../../core/common/dto/mongo.peer.id.dto";
import {CreateReportSystemDto} from "../report_system/dto/create-report_system.dto";


@V1Controller("profile")
export class ProfileController {
    constructor(private readonly profileService: ProfileService) {
    }


    @UseGuards(VerifiedAuthGuard)
    @Get("/")
    async getMyProfile(@Req() req: any) {
        return resOK(await this.profileService.getMyProfile(req.user));
    }


    @Get("/app-config")
    async getConfig(@Req() req: any) {
        return resOK(await this.profileService.getAppConfig(req.user));
    }

    @UseGuards(VerifiedAuthGuard)
    @Get("/users")
    async getUsersAndSearch(@Req() req: any, @Query() dto: Object) {
        return resOK(await this.profileService.getUsersAndSearch(dto, req.user));
    }

    @UseGuards(VerifiedAuthGuard)
    @Get("/admin-notifications")
    async getAdminNotification(@Req() req: any, @Query() dto: Object) {
        return resOK(await this.profileService.getAdminNotification(dto));
    }

    @UseGuards(VerifiedAuthGuard)
    @Get("/device")
    async getMyDevice(@Req() req: any) {
        return resOK(await this.profileService.getMyDevices(req.user));
    }

    @UseGuards(VerifiedAuthGuard)
    @Delete("/device/:id")
    async deleteDevice(
        @Req() req: any,
        @Param() dto: MongoIdDto,
        @Body('password') password: string
    ) {
        dto.myUser = req.user
        return resOK(await this.profileService.deleteDevice(dto, password));
    }

    @UseGuards(VerifiedAuthGuard)
    @Patch("/name")
    async updateMyName(@Req() req: any, @Body() dto: UpdateMyNameDto) {
        dto.myUser = req.user;
        return resOK(await this.profileService.updateMyName(dto));
    }


    @UseGuards(VerifiedAuthGuard)
    @Patch("/privacy")
    async updateMyPrivacy(@Req() req: any, @Body() dto: UpdateMyPrivacyDto) {
        dto.myUser = req.user;
        return resOK(await this.profileService.updateMyPrivacy(dto));
    }


    @UseGuards(VerifiedAuthGuard)
    @Get("/blocked")
    async getMyBlocked(@Req() req: any, @Query() dto: Object) {
        return resOK(await this.profileService.getMyBlocked(req.user, dto));
    }

    @UseGuards(VerifiedAuthGuard)
    @Patch("/bio")
    async updateMyBio(@Req() req: any, @Body() dto: UpdateMyBioDto) {
        dto.myUser = req.user;
        return resOK(await this.profileService.updateMyBio(dto));
    }

    @UseGuards(VerifiedAuthGuard)
    @Patch("/phone-number")
    async updateMyPhoneNumber(@Req() req: any, @Body() dto: UpdateMyPhoneNumberDto) {
        dto.myUser = req.user;
        return resOK(await this.profileService.updateMyPhoneNumber(dto));
    }

    @UseGuards(VerifiedAuthGuard)
    @Patch("/password")
    async updateMyPassword(@Req() req: any, @Body() dto: UpdateMyPasswordDto) {
        dto.myUser = req.user;
        return resOK(await this.profileService.updateMyPassword(dto));
    }

    @UseGuards(VerifiedAuthGuard)
    @UseInterceptors(imageFileInterceptor)
    @Patch("/image")
    async updateMyImage(@Req() req: any, @UploadedFile() file?: any) {
        if (!file) {
            throw new BadRequestException("Image is required!");
        }
        return resOK(await this.profileService.updateMyImage(file, req.user));
    }
    @UseGuards(VerifiedAuthGuard)
    @Get("/chat-request")
    async getMyChatRequest(@Req() req: any,   @Query() dto: object) {
        return resOK(await this.profileService.getMyChatRequest(req.user,dto));
    }
    @Get("/public/:id")
    async getPublicProfile(@Param() dto: MongoIdDto) {
        return resOK(await this.profileService.getPublicProfile(dto));
    }

    // Web page endpoint for testing profile links
    @Get("/profile/:id")
    async getProfilePage(@Param() dto: MongoIdDto, @Res() res: any) {
        try {
            const profile = await this.profileService.getPublicProfile(dto);
            const html = `
<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>${profile.fullName} - Orbit Profile</title>
    <style>
        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            margin: 0;
            padding: 20px;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            display: flex;
            align-items: center;
            justify-content: center;
        }
        .profile-card {
            background: white;
            border-radius: 20px;
            padding: 40px;
            text-align: center;
            box-shadow: 0 20px 40px rgba(0,0,0,0.1);
            max-width: 400px;
            width: 100%;
        }
        .profile-image {
            width: 120px;
            height: 120px;
            border-radius: 50%;
            margin: 0 auto 20px;
            background: #f0f0f0;
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 48px;
            color: #666;
        }
        .profile-name {
            font-size: 24px;
            font-weight: bold;
            margin-bottom: 10px;
            color: #333;
        }
        .profile-bio {
            color: #666;
            margin-bottom: 30px;
            line-height: 1.5;
        }
        .badge {
            display: inline-block;
            background: #4CAF50;
            color: white;
            padding: 4px 12px;
            border-radius: 12px;
            font-size: 12px;
            margin-bottom: 20px;
        }
        .download-section {
            margin-top: 30px;
            padding-top: 30px;
            border-top: 1px solid #eee;
        }
        .download-title {
            font-size: 18px;
            margin-bottom: 20px;
            color: #333;
        }
        .download-buttons {
            display: flex;
            gap: 10px;
            justify-content: center;
            flex-wrap: wrap;
        }
        .download-btn {
            display: inline-block;
            padding: 12px 24px;
            background: #007AFF;
            color: white;
            text-decoration: none;
            border-radius: 8px;
            font-weight: 500;
            transition: background 0.3s;
        }
        .download-btn:hover {
            background: #0056CC;
        }
        .android-btn {
            background: #34A853;
        }
        .android-btn:hover {
            background: #2E7D32;
        }
        .test-note {
            margin-top: 20px;
            padding: 15px;
            background: #fff3cd;
            border: 1px solid #ffeaa7;
            border-radius: 8px;
            color: #856404;
            font-size: 14px;
        }
    </style>
</head>
<body>
    <div class="profile-card">
        <div class="profile-image">
            ${profile.userImage ? `<img src="${profile.userImage}" alt="${profile.fullName}" style="width: 100%; height: 100%; border-radius: 50%; object-fit: cover;">` : '👤'}
        </div>

        ${profile.hasBadge ? '<div class="badge">✓ Verified</div>' : ''}

        <div class="profile-name">${profile.fullName}</div>

        <div class="profile-bio">
            ${profile.bio || 'Hi! I am using Orbit'}
        </div>

        <div class="test-note">
            <strong>🧪 Test Mode</strong><br>
            This is a local development profile page for testing the share profile functionality.
        </div>

        <div class="download-section">
            <div class="download-title">Download Orbit</div>
            <div class="download-buttons">
                <a href="https://play.google.com/store/apps/details?id=com.orbit.ke" class="download-btn android-btn">
                    Android
                </a>
                <a href="https://apps.apple.com/us/app/orbitt-chat/id6748567153" class="download-btn">
                    iOS
                </a>
            </div>
        </div>
    </div>
</body>
</html>`;
            res.setHeader('Content-Type', 'text/html');
            res.send(html);
        } catch (error) {
            const errorHtml = `
<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Profile Not Found - Orbit</title>
    <style>
        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            margin: 0;
            padding: 20px;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            display: flex;
            align-items: center;
            justify-content: center;
        }
        .error-card {
            background: white;
            border-radius: 20px;
            padding: 40px;
            text-align: center;
            box-shadow: 0 20px 40px rgba(0,0,0,0.1);
            max-width: 400px;
            width: 100%;
        }
        .error-icon {
            font-size: 64px;
            margin-bottom: 20px;
        }
        .error-title {
            font-size: 24px;
            font-weight: bold;
            margin-bottom: 10px;
            color: #333;
        }
        .error-message {
            color: #666;
            margin-bottom: 30px;
        }
    </style>
</head>
<body>
    <div class="error-card">
        <div class="error-icon">😕</div>
        <div class="error-title">Profile Not Found</div>
        <div class="error-message">
            This profile doesn't exist or is not available.
        </div>
    </div>
</body>
</html>`;
            res.setHeader('Content-Type', 'text/html');
            res.status(404).send(errorHtml);
        }
    }

    @UseGuards(VerifiedAuthGuard)
    @Get("/:id")
    async getPeerProfile(@Req() req: any, @Param() dto: MongoIdDto) {
        dto.myUser = req.user;
        return resOK(await this.profileService.getPeerProfile(dto));
    }

    @UseGuards(VerifiedAuthGuard)
    @Post("/:id/chat-request")
    async sendChatRequest(@Req() req: any, @Param() dto: MongoIdDto) {
        dto.myUser = req.user;
        return resOK(await this.profileService.sendChatRequest(dto));
    }

    @UseGuards(VerifiedAuthGuard)
    @Patch("/:id/chat-request")
    async updateChatRequest(
        @Req() req: any,
        @Param() dto: MongoIdDto,
        @Body( ) status: UpdateChatReqStatusDto
    ) {
        dto.myUser = req.user;
        return resOK(await this.profileService.updateChatRequest(dto,status));
    }


    @UseGuards(VerifiedAuthGuard)
    @Delete("/push")
    async deleteFcm(@Req() req: any) {
        return resOK(await this.profileService.deleteFcmFor(req.user));
    }

    @UseGuards(VerifiedAuthGuard)
    @Post("/push")
    async addFcm(
        @Req() req: any,
        @Body("pushKey") pushKey?: string,
        @Body("voipKey") voipKey?: string,

    ) {
        if (!pushKey && !voipKey) {
            throw new BadRequestException("pushKey or voipKey is required");
        }
        return resOK(await this.profileService.addPushKey(req.user, pushKey, voipKey));
    }

    @UseGuards(VerifiedAuthGuard)
    @Patch("/push")
    async updateFcm(@Body("pushKey") pushKey: String, @Req() req: any) {
        if (!pushKey) {
            throw new BadRequestException("pushKey is required");
        }
        return resOK(await this.profileService.updateFcm(req.user, pushKey));
    }

    @UseGuards(VerifiedAuthGuard)
    @Patch("/lang")
    async updateLanguage(@Body("lang") lang: String, @Req() req: any) {
        if (!lang) {
            throw new BadRequestException("lang is required");
        }
        return resOK(await this.profileService.updateLanguage(req.user, lang));
    }

    @UseGuards(VerifiedAuthGuard)
    @Patch("/visit")
    async setVisit(@Req() req: any) {
        return resOK(await this.profileService.setVisit(req.user));
    }

    @UseGuards(VerifiedAuthGuard)
    @Get("/users/:peerId/last-seen")
    async getUserLastSeenAt(
        @Req() req: any,
        @Param() dto: MongoPeerIdDto,
    ) {
        dto.myUser = req.user
        return resOK(await this.profileService.getUserLastSeenAt(dto));
    }

    @UseGuards(VerifiedAuthGuard)
    @Patch("/password")
    async updatePassword(@Body() dto: UpdatePasswordDto, @Req() req: any) {
        dto.myUser = req.user;
        return resOK(await this.profileService.updatePassword(req.user, dto));
    }


    @UseGuards(VerifiedAuthGuard)
    @Delete("/delete-my-account")
    async deleteMyAccount(@Req() req: any, @Body('password') password: string) {
        return resOK(await this.profileService.deleteMyAccount(req.user, password));
    }

    @UseGuards(VerifiedAuthGuard)
    @Patch("/version")
    async checkVersion(@Req() req: any, @Body() dto: CheckVersionDto) {
        dto.myUser = req.user;
        return resOK(await this.profileService.checkVersion(dto));
    }

    @UseGuards(VerifiedAuthGuard)
    @Post("/report")
    async createReport(@Req() req: any, @Body() dto: CreateReportSystemDto) {
        dto.myUser = req.user;
        return resOK(await this.profileService.createReport(dto));
    }

    @UseGuards(VerifiedAuthGuard)
    @Get("/loyalty-points")
    async getUserLoyaltyPoints(@Req() req: any) {
        return resOK(await this.profileService.getUserLoyaltyPoints(req.user));
    }

    // Test endpoint to get user IDs for testing profile links
    @Get("/test/users")
    async getTestUsers() {
        return resOK(await this.profileService.getTestUsers());
    }
}
