#!/bin/bash

# Test script for profile link functionality
# This script helps you quickly test the share profile feature

echo "🧪 Profile Link Testing Script"
echo "=============================="
echo ""

# Check if backend is running
echo "1. Checking if backend is running..."
if curl -s http://localhost:3000/api/v1/profile/app-config > /dev/null; then
    echo "✅ Backend is running on localhost:3000"
else
    echo "❌ Backend is not running. Please start it with:"
    echo "   cd back/backend && npm run start:dev"
    exit 1
fi

echo ""
echo "2. Getting test users..."

# Get test users
RESPONSE=$(curl -s http://localhost:3000/api/v1/profile/test/users)

if echo "$RESPONSE" | grep -q "success"; then
    echo "✅ Test users endpoint is working"
    echo ""
    echo "📋 Available test users:"
    echo "$RESPONSE" | python3 -m json.tool 2>/dev/null || echo "$RESPONSE"
    echo ""
    
    # Extract first user ID for testing
    USER_ID=$(echo "$RESPONSE" | grep -o '"id":"[^"]*"' | head -1 | cut -d'"' -f4)
    
    if [ ! -z "$USER_ID" ]; then
        echo "🔗 Test profile links:"
        echo "   iOS Simulator: http://localhost:3000/profile/$USER_ID"
        echo "   Android Emulator: http://********:3000/profile/$USER_ID"
        echo "   Browser: http://localhost:3000/profile/$USER_ID"
        echo ""
        
        echo "3. Testing profile page..."
        if curl -s "http://localhost:3000/profile/$USER_ID" | grep -q "Orbit Profile"; then
            echo "✅ Profile page is working"
            echo ""
            echo "🎉 Everything is set up correctly!"
            echo ""
            echo "📱 How to test:"
            echo "   1. Run your Flutter app"
            echo "   2. Go to Settings → Share Profile"
            echo "   3. The shared link will use your local backend"
            echo "   4. Open the link in a browser to see the profile page"
            echo "   5. Test deep linking by opening the link on a device with the app"
        else
            echo "❌ Profile page is not working properly"
        fi
    else
        echo "❌ Could not extract user ID from response"
    fi
else
    echo "❌ Test users endpoint is not working"
    echo "Response: $RESPONSE"
fi

echo ""
echo "📖 For more details, see: back/backend/test-profile-link.md"
