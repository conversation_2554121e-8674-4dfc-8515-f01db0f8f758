# Testing Share Profile Link Functionality

This guide helps you test the share profile link functionality locally without deploying the backend.

## What was changed:

1. **Flutter App Changes:**
   - Updated `shareMyProfile()` in `settings_tab_controller.dart` to use local development URL
   - Updated `_shareProfile()` in `public_profile_view.dart` to use local development URL
   - Updated deep link handler to work with local URLs

2. **Backend Changes:**
   - Added a new endpoint `/profile/:id` that serves an HTML page for testing
   - This endpoint shows a nice profile page with user info and download links

## How to test:

### Step 1: Start your backend server
```bash
cd /Users/<USER>/Desktop/source2/back/backend
npm run start:dev
```

### Step 2: Test the profile page in browser
Open your browser and go to:
- `http://localhost:3000/profile/USER_ID_HERE` (replace USER_ID_HERE with an actual user ID)

### Step 3: Test in Flutter app
1. Run your Flutter app on iOS simulator or Android emulator
2. Go to Settings → Share Profile
3. The shared link will now be something like:
   - iOS: `http://localhost:3000/profile/USER_ID`
   - Android: `http://********:3000/profile/USER_ID`

### Step 4: Test the link
1. Copy the shared link from the share dialog
2. Paste it in a browser to see the profile page
3. Or send it to another device/simulator to test deep linking

## Expected behavior:

1. **In browser:** You should see a nice profile page with:
   - User's profile picture (if available)
   - User's name and bio
   - Verification badge (if user has one)
   - Download links for Android/iOS
   - A note indicating this is test mode

2. **Deep linking:** When you tap the link on a device with the app installed, it should open the app and navigate to the user's profile.

## Troubleshooting:

1. **Profile not found:** Make sure the user ID in the URL exists in your database
2. **Can't access localhost:** Make sure your backend is running on the correct port (3000)
3. **Deep linking not working:** Make sure your app is properly configured for deep links

## Notes:

- The profile page includes a "Test Mode" indicator to show this is for development
- The URLs automatically adapt based on platform (localhost for iOS, ******** for Android emulator)
- When you deploy to production, you can revert these changes to use the production URLs
